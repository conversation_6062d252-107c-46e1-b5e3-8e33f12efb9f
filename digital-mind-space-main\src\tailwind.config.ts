import type { Config } from "tailwindcss";

export default {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px'
      }
    },
    extend: {
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        display: ['Poppins', 'system-ui', 'sans-serif'],
      },
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))'
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))'
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))'
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))'
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))'
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))'
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))'
        },
        sidebar: {
          DEFAULT: 'hsl(var(--sidebar-background))',
          foreground: 'hsl(var(--sidebar-foreground))',
          primary: 'hsl(var(--sidebar-primary))',
          'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
          accent: 'hsl(var(--sidebar-accent))',
          'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
          border: 'hsl(var(--sidebar-border))',
          ring: 'hsl(var(--sidebar-ring))'
        },
        glass: {
          background: "rgba(255, 255, 255, 0.05)",
          border: "rgba(255, 255, 255, 0.08)",
        },
        vibrant: {
          blue: "#4C6FFF",
          purple: "#9D50FF",
          pink: "#FF4ECD",
          orange: "#FF6B2C",
          amber: "#FFAD0F",
          green: "#05CD99",
          cyan: "#13D0E4",
          red: "#FF4A55",
        },
        neutral: {
          100: '#F6F6F7',
          200: '#E9E9EC',
          300: '#C8C8C9',
          400: '#9F9EA1',
          500: '#8A898C',
          600: '#6B6A6D',
          700: '#403E43',
          800: '#2D2B30',
          900: '#221F26',
        },
        pastel: {
          blue: "#C7DEFF",
          purple: "#DFCBFF",
          pink: "#FFCBF3",
          green: "#C3FFE8",
          yellow: "#FFF4C7",
          cyan: "#C3FCFF",
          indigo: "#D0D1FF",
          amber: "#FFE3A6",
          red: "#FFCBD0",
        },
        status: {
          success: '#05CD99',
          warning: '#FFAD0F',
          error: '#FF4A55',
          info: '#4C6FFF',
        },
        gradientStart: {
          neutral: '#94A3B8',
          slate: '#64748B',
          purple: '#9D50FF',
          blue: '#4C6FFF',
          pink: '#FF4ECD',
          green: '#05CD99',
          amber: '#FFAD0F',
          red: '#FF4A55',
          indigo: '#635BFF',
          cyan: '#13D0E4',
          sunrise: '#FF6B2C',
          sunset: '#FF4ECD',
        },
        gradientEnd: {
          neutral: '#64748B',
          slate: '#475569',
          purple: '#7C3AED',
          blue: '#2563EB',
          pink: '#DB2777',
          green: '#059669',
          amber: '#D97706',
          red: '#DC2626',
          indigo: '#4F46E5',
          cyan: '#06ADC9',
          sunrise: '#FFAD0F',
          sunset: '#9D50FF',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)'
      },
      keyframes: {
        'accordion-down': {
          from: {
            height: '0'
          },
          to: {
            height: 'var(--radix-accordion-content-height)'
          }
        },
        'accordion-up': {
          from: {
            height: 'var(--radix-accordion-content-height)'
          },
          to: {
            height: '0'
          }
        },
        'fade-in': {
          '0%': {
            opacity: '0',
            transform: 'translateY(10px)'
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)'
          }
        },
        'fade-out': {
          '0%': {
            opacity: '1',
            transform: 'translateY(0)'
          },
          '100%': {
            opacity: '0',
            transform: 'translateY(10px)'
          }
        },
        'scale-in': {
          '0%': {
            transform: 'scale(0.95)',
            opacity: '0'
          },
          '100%': {
            transform: 'scale(1)',
            opacity: '1'
          }
        },
        'scale-out': {
          from: { transform: 'scale(1)', opacity: '1' },
          to: { transform: 'scale(0.95)', opacity: '0' }
        },
        'slide-in-right': {
          '0%': { transform: 'translateX(100%)' },
          '100%': { transform: 'translateX(0)' }
        },
        'slide-out-right': {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(100%)' }
        },
        pulse: {
          '0%, 100%': {
            opacity: '1'
          },
          '50%': {
            opacity: '0.5'
          }
        },
        'floating': {
          '0%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-5px)' },
          '100%': { transform: 'translateY(0px)' }
        },
        'glow': {
          '0%': { boxShadow: '0 0 5px rgba(255, 255, 255, 0.3)' },
          '50%': { boxShadow: '0 0 20px rgba(255, 255, 255, 0.5)' },
          '100%': { boxShadow: '0 0 5px rgba(255, 255, 255, 0.3)' }
        },
        'shimmer': {
          '0%': { backgroundPosition: '-200% 0' },
          '100%': { backgroundPosition: '200% 0' },
        },
        'data-flow': {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' }
        },
        'breathe': {
          '0%, 100%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(1.05)' }
        }
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'fade-in': 'fade-in 0.3s ease-out',
        'fade-out': 'fade-out 0.3s ease-out',
        'scale-in': 'scale-in 0.2s ease-out',
        'scale-out': 'scale-out 0.2s ease-out',
        'slide-in-right': 'slide-in-right 0.3s ease-out',
        'slide-out-right': 'slide-out-right 0.3s ease-out',
        'enter': 'fade-in 0.3s ease-out, scale-in 0.2s ease-out',
        'exit': 'fade-out 0.3s ease-out, scale-out 0.2s ease-out',
        pulse: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        floating: 'floating 3s ease-in-out infinite',
        glow: 'glow 2s infinite',
        shimmer: 'shimmer 2s linear infinite',
        'data-flow': 'data-flow 1.5s ease-in-out infinite',
        'breathe': 'breathe 3s ease-in-out infinite',
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-subtle': 'linear-gradient(to right, #C7D2FE, #E2E8F0)',
        'gradient-pastel': 'linear-gradient(135deg, rgba(200, 200, 210, 0.3) 0%, rgba(220, 220, 230, 0.3) 100%)',
        'gradient-neutral': 'linear-gradient(102.3deg, rgba(80, 82, 90, 0.7) 5.9%, rgba(100, 102, 110, 0.7) 60%, rgba(120, 122, 130, 0.7) 89%)',
        'gradient-blue': 'linear-gradient(135deg, rgba(76, 111, 255, 0.5) 0%, rgba(37, 99, 235, 0.5) 100%)',
        'gradient-deep': 'linear-gradient(180deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.8) 100%)',
        'neutral-radial': 'radial-gradient(circle at 50% 50%, rgba(150, 150, 160, 0.2) 0%, rgba(120, 120, 130, 0.1) 40%, transparent 70%)',
        'light-radial': 'radial-gradient(circle at top right, rgba(255, 255, 255, 0.03) 0%, transparent 70%)',
        'shimmer-gradient': 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.08), transparent)',
        'gradient-vibrant': 'linear-gradient(135deg, rgba(157, 80, 255, 0.6) 0%, rgba(255, 78, 205, 0.6) 100%)',
        'gradient-sunrise': 'linear-gradient(135deg, rgba(255, 107, 44, 0.6) 0%, rgba(255, 173, 15, 0.6) 100%)',
        'gradient-sunset': 'linear-gradient(135deg, rgba(255, 78, 205, 0.6) 0%, rgba(157, 80, 255, 0.6) 100%)',
        'gradient-ocean': 'linear-gradient(135deg, rgba(19, 208, 228, 0.6) 0%, rgba(76, 111, 255, 0.6) 100%)',
        'gradient-forest': 'linear-gradient(135deg, rgba(5, 205, 153, 0.6) 0%, rgba(19, 208, 228, 0.6) 100%)',
        'gradient-pastel-new': 'linear-gradient(135deg, rgba(199, 222, 255, 0.6) 0%, rgba(223, 203, 255, 0.6) 100%)',
        'bg-glass-1': 'url(/lovable-uploads/8f230fbe-d8d7-4250-a487-66296e02e936.png)',
        'bg-glass-2': 'url(/lovable-uploads/f187e5cc-ab50-4d9e-89c2-eeae7cc5cd3c.png)',
        'bg-glass-3': 'url(/lovable-uploads/19bb8d9a-dd96-4ee3-a911-49cda707c380.png)',
        'bg-glass-4': 'url(/lovable-uploads/c11ee040-8b3c-4be9-9681-8cb80db50eb5.png)',
        'bg-glass-5': 'url(/lovable-uploads/90c3c22d-b06d-4507-8fc3-8856b11577a9.png)',
        'bg-glass-6': 'url(/lovable-uploads/41b2debe-b13d-4824-a824-2cbd644e8940.png)',
        'bg-glass-7': 'url(/lovable-uploads/9c84cc90-e62e-456a-b37d-f1a0b6b65f04.png)',
        'bg-glass-8': 'url(/lovable-uploads/2f8f9cf8-36d4-4ca9-a76b-0d9ab59202a7.png)',
      },
      backgroundSize: {
        '300': '300% 300%',
      },
    }
  },
  plugins: [
    // @ts-ignore - Tailwind plugins are CommonJS
    require("tailwindcss-animate")
  ],
} satisfies Config;
