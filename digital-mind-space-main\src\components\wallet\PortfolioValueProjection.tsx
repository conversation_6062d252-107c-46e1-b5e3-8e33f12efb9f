import React, { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, Info } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// --- Configuration ---
const CURRENT_VALUE = 125000; // Example starting value (replace with prop later)
const YEARS = [1, 3, 5]; // Projection years
const GROWTH_RATES = { // Annual growth rates
  conservative: 0.04,
  moderate: 0.07,
  optimistic: 0.11,
};
const CHART_WIDTH = 300;
const CHART_HEIGHT = 180;
const PADDING = { top: 20, right: 10, bottom: 30, left: 50 }; // Adjusted left padding for labels

// --- Types ---
interface ProjectionDataPoint {
  year: number;
  conservative: number;
  moderate: number;
  optimistic: number;
}

// --- Helper: Calculate Compound Growth ---
function calculateProjection(initial: number, rate: number, years: number): number {
  return initial * Math.pow(1 + rate, years);
}

// --- Component ---
export function PortfolioValueProjection() {
  // --- State ---
  const [projectionData, setProjectionData] = useState<ProjectionDataPoint[]>([]);
  const [maxValue, setMaxValue] = useState(CURRENT_VALUE);

  // --- Data Calculation Effect ---
  useEffect(() => {
    const data: ProjectionDataPoint[] = [];
    let maxVal = CURRENT_VALUE;
    
    // Add initial point (Year 0)
    data.push({
        year: 0,
        conservative: CURRENT_VALUE,
        moderate: CURRENT_VALUE,
        optimistic: CURRENT_VALUE
    });

    YEARS.forEach(year => {
      const conservative = calculateProjection(CURRENT_VALUE, GROWTH_RATES.conservative, year);
      const moderate = calculateProjection(CURRENT_VALUE, GROWTH_RATES.moderate, year);
      const optimistic = calculateProjection(CURRENT_VALUE, GROWTH_RATES.optimistic, year);
      
      data.push({ year, conservative, moderate, optimistic });
      maxVal = Math.max(maxVal, optimistic); // Track max value for Y-axis scaling
    });

    setProjectionData(data);
    // Add some buffer to max value for chart appearance
    setMaxValue(maxVal * 1.1);
  }, []); // Run once on mount

  // --- SVG Scaling Functions ---
  const scaleX = (year: number) => {
    const lastYear = YEARS[YEARS.length - 1] || 1;
    return PADDING.left + (year / lastYear) * (CHART_WIDTH - PADDING.left - PADDING.right);
  };
  const scaleY = (value: number) => {
    return PADDING.top + (1 - value / maxValue) * (CHART_HEIGHT - PADDING.top - PADDING.bottom);
  };

  // --- Path Generation ---
  const generatePath = (key: 'conservative' | 'moderate' | 'optimistic') => {
    if (projectionData.length === 0) return "";
    let path = `M ${scaleX(0)} ${scaleY(CURRENT_VALUE)}`;
    projectionData.slice(1).forEach(d => {
      path += ` L ${scaleX(d.year)} ${scaleY(d[key])}`;
    });
    return path;
  };
  
  const paths = {
      conservative: generatePath('conservative'),
      moderate: generatePath('moderate'),
      optimistic: generatePath('optimistic'),
  };

  const pathVariants = {
    hidden: { pathLength: 0, opacity: 0 },
    visible: {
      pathLength: 1,
      opacity: 1,
      transition: { duration: 1.5, ease: "easeInOut" }
    }
  };

  // --- Y-Axis Labels ---
  const yAxisLabels = useMemo(() => {
    const numTicks = 5;
    const ticks = [];
    for (let i = 0; i <= numTicks; i++) {
        const value = (maxValue / numTicks) * i;
        ticks.push({
            value: value,
            y: scaleY(value)
        });
    }
    return ticks;
  }, [maxValue, scaleY]);

  return (
    <TooltipProvider delayDuration={100}>
      <motion.div
        className="bg-gradient-to-br from-[#262646]/70 via-[#1a1a33]/80 to-[#262646]/70 border border-white/10 rounded-2xl flex flex-col shadow-inner shadow-black/40 relative overflow-hidden h-full p-6"
        style={{ perspective: '1000px' }}
      >
        {/* Header */} 
        <div className="relative z-10 mb-4">
          <h3 className="text-lg font-semibold text-white flex items-center">
            <TrendingUp className="h-5 w-5 mr-2 text-green-400" />
            Portfolio Projection
          </h3>
          <p className="text-xs text-neutral-400">Estimated growth over {YEARS[YEARS.length - 1]} years (simulated)</p>
        </div>

        {/* 3D Chart Area */} 
        <motion.div 
            className="relative flex-grow flex items-center justify-center"
            initial={{ opacity: 0, y: 20, rotateX: -5 }} // Initial slight tilt
            animate={{ opacity: 1, y: 0, rotateX: -5 }} // Keep slight tilt
            transition={{ duration: 0.5, delay: 0.2 }}
            style={{ transformStyle: 'preserve-3d' }}
        >
          <svg 
            width={CHART_WIDTH} 
            height={CHART_HEIGHT} 
            viewBox={`0 0 ${CHART_WIDTH} ${CHART_HEIGHT}`} 
            className="overflow-visible"
          >
            {/* Axes and Grid Lines */} 
            <g className="text-[9px] fill-neutral-500">
               {/* Y-Axis Labels & Grid Lines */} 
               {yAxisLabels.map((tick, i) => (
                  <g key={`y-tick-${i}`}>
                    <text 
                        x={PADDING.left - 8} 
                        y={tick.y + 3} // Offset for better alignment
                        textAnchor="end"
                        className="opacity-80"
                    >
                        {`$${(tick.value / 1000).toFixed(0)}k`}
                    </text>
                    <line 
                        x1={PADDING.left} x2={CHART_WIDTH - PADDING.right} 
                        y1={tick.y} y2={tick.y} 
                        stroke="currentColor" 
                        strokeWidth="0.5"
                        opacity={0.1}
                    />
                  </g>
               ))}
               {/* X-Axis Labels & Grid Lines */} 
               {projectionData.map((d, i) => (
                   <g key={`x-tick-${i}`}>
                    <text 
                        x={scaleX(d.year)} 
                        y={CHART_HEIGHT - PADDING.bottom + 15} 
                        textAnchor="middle"
                        className="opacity-80"
                    >
                        {d.year === 0 ? 'Now' : `Yr ${d.year}`}
                    </text>
                    {d.year > 0 && (
                         <line 
                            x1={scaleX(d.year)} x2={scaleX(d.year)} 
                            y1={PADDING.top} y2={CHART_HEIGHT - PADDING.bottom} 
                            stroke="currentColor" 
                            strokeWidth="0.5"
                            opacity={0.05}
                        />
                    )}
                   </g>
               ))}
               {/* X Axis Line */} 
               <line 
                 x1={PADDING.left} x2={CHART_WIDTH - PADDING.right} 
                 y1={CHART_HEIGHT - PADDING.bottom} y2={CHART_HEIGHT - PADDING.bottom} 
                 stroke="currentColor" strokeWidth="0.5" opacity={0.3}
               />
            </g>

            {/* Projection Paths */} 
            <defs>
                <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
                    <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                    <feMerge>
                        <feMergeNode in="coloredBlur"/>
                        <feMergeNode in="SourceGraphic"/>
                    </feMerge>
                </filter>
            </defs>
            
            {/* Conservative Path */} 
            <motion.path
              d={paths.conservative}
              fill="none"
              stroke="rgba(96, 165, 250, 0.7)" // Blue
              strokeWidth={2}
              variants={pathVariants}
              initial="hidden"
              animate="visible"
              transition={{ delay: 0.2, duration: 1.5, ease: "easeInOut" }}
            />
            {/* Moderate Path */} 
            <motion.path
              d={paths.moderate}
              fill="none"
              stroke="rgba(168, 85, 247, 0.8)" // Purple
              strokeWidth={2.5} // Slightly thicker
              variants={pathVariants}
              initial="hidden"
              animate="visible"
              transition={{ delay: 0.4, duration: 1.5, ease: "easeInOut" }}
              // style={{ filter: "url(#glow)" }} // Optional glow
            />
            {/* Optimistic Path */} 
            <motion.path
              d={paths.optimistic}
              fill="none"
              stroke="rgba(52, 211, 153, 0.9)" // Green
              strokeWidth={2}
              variants={pathVariants}
              initial="hidden"
              animate="visible"
              transition={{ delay: 0.6, duration: 1.5, ease: "easeInOut" }}
            />
          </svg>
        </motion.div>

        {/* Legend */} 
        <div className="relative z-10 mt-4 flex justify-center gap-4 text-xs">
            <div className="flex items-center gap-1.5">
                <div className="w-3 h-1.5 rounded-full bg-[rgba(96,165,250,0.7)]"></div><span className="text-neutral-400">Conservative</span>
            </div>
            <div className="flex items-center gap-1.5">
                <div className="w-3 h-1.5 rounded-full bg-[rgba(168,85,247,0.8)]"></div><span className="text-neutral-400">Moderate</span>
            </div>
            <div className="flex items-center gap-1.5">
                <div className="w-3 h-1.5 rounded-full bg-[rgba(52,211,153,0.9)]"></div><span className="text-neutral-400">Optimistic</span>
            </div>
        </div>
        
      </motion.div>
    </TooltipProvider>
  );
} 