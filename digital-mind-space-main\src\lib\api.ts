// API service for agent management
export interface UserAgent {
  id: string;
  name: string;
  specialty: string;
  market: 'equity' | 'crypto' | 'forex';
  dateAdded: string;
  isActive: boolean;
  settings?: Record<string, unknown>;
}

export interface TradeSignal {
  action: 'buy' | 'sell' | 'hold';
  symbol: string;
  quantity: number;
  price?: number;
  orderType?: 'market' | 'limit' | 'stop';
  stopLoss?: number;
  takeProfit?: number;
  confidence?: number;
  metadata?: Record<string, unknown>;
}

export interface AgentPerformance {
  totalReturn: number;
  winRate: number;
  sharpeRatio: number;
  maxDrawdown: number;
  totalTrades: number;
}

export interface AgentSettings {
  nexus: {
    strategyAggressiveness: string;
    tradingStyle: string;
    enableTechnicalAnalysis: boolean;
    enableFundamentalAnalysis: boolean;
    maxPositionSize: number;
    positionSizeType: string;
    dailyLossLimit: number;
    dailyLossAction: string;
    assetsToAvoid: string[];
    maxAssetWeight: number;
    rebalancingFrequency: string;
    enableSmartAllocation: boolean;
    customRules: string;
  };
  oracle: {
    strategyAggressiveness: string;
    predictionTimeframe: string;
    enableMarketSentiment: boolean;
    enableNewsAnalysis: boolean;
    maxPositionSize: number;
    positionSizeType: string;
    dailyLossLimit: number;
    dailyLossAction: string;
    assetsToAvoid: string[];
    maxAssetWeight: number;
    rebalancingFrequency: string;
    enableSmartAllocation: boolean;
    customRules: string;
  };
  echo: {
    strategyAggressiveness: string;
    enableAlgorithmicTrading: boolean;
    enableBacktesting: boolean;
    enablePaperTrading: boolean;
    maxPositionSize: number;
    positionSizeType: string;
    dailyLossLimit: number;
    dailyLossAction: string;
    assetsToAvoid: string[];
    maxAssetWeight: number;
    rebalancingFrequency: string;
    enableSmartAllocation: boolean;
    customRules: string;
  };
  sentinel: {
    strategyAggressiveness: string;
    enableComplianceChecks: boolean;
    enableAuditTrail: boolean;
    enableRiskMonitoring: boolean;
    maxPositionSize: number;
    positionSizeType: string;
    dailyLossLimit: number;
    dailyLossAction: string;
    assetsToAvoid: string[];
    maxAssetWeight: number;
    rebalancingFrequency: string;
    enableSmartAllocation: boolean;
    customRules: string;
  };
}

// API base URL - uses environment variable or defaults to localhost
const API_BASE_URL = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_API_URL) || 'http://localhost:3001/api';

// API client with error handling
class ApiClient {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.warn('API Request failed, falling back to localStorage:', error);
      throw error;
    }
  }

  // Agent Management
  async getUserAgents(): Promise<UserAgent[]> {
    try {
      return await this.request<UserAgent[]>('/agents');
    } catch (error) {
      // Fallback to localStorage for demo
      const stored = localStorage.getItem('userAgents');
      return stored ? JSON.parse(stored) : [];
    }
  }

  async addAgent(agentData: Omit<UserAgent, 'id' | 'dateAdded'>): Promise<UserAgent> {
    const newAgent: UserAgent = {
      ...agentData,
      id: `agent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      dateAdded: new Date().toISOString(),
    };

    try {
      return await this.request<UserAgent>('/agents', {
        method: 'POST',
        body: JSON.stringify(newAgent),
      });
    } catch (error) {
      // Fallback to localStorage for demo
      const stored = localStorage.getItem('userAgents');
      const agents = stored ? JSON.parse(stored) : [];
      agents.push(newAgent);
      localStorage.setItem('userAgents', JSON.stringify(agents));
      return newAgent;
    }
  }

  async removeAgent(agentId: string): Promise<void> {
    try {
      await this.request(`/agents/${agentId}`, {
        method: 'DELETE',
      });
    } catch (error) {
      // Fallback to localStorage for demo
      const stored = localStorage.getItem('userAgents');
      if (stored) {
        const agents = JSON.parse(stored);
        const filtered = agents.filter((agent: UserAgent) => agent.id !== agentId);
        localStorage.setItem('userAgents', JSON.stringify(filtered));
      }
    }
  }

  async updateAgentStatus(agentId: string, isActive: boolean): Promise<void> {
    try {
      await this.request(`/agents/${agentId}/status`, {
        method: 'PATCH',
        body: JSON.stringify({ isActive }),
      });
    } catch (error) {
      // Fallback to localStorage for demo
      const stored = localStorage.getItem('userAgents');
      if (stored) {
        const agents = JSON.parse(stored);
        const updated = agents.map((agent: UserAgent) =>
          agent.id === agentId ? { ...agent, isActive } : agent
        );
        localStorage.setItem('userAgents', JSON.stringify(updated));
      }
    }
  }

  // Settings Management
  async getAgentSettings(): Promise<AgentSettings> {
    try {
      return await this.request<AgentSettings>('/settings');
    } catch (error) {
      // Fallback to localStorage for demo
      const stored = localStorage.getItem('agentSettings');
      return stored ? JSON.parse(stored) : this.getDefaultSettings();
    }
  }

  async updateAgentSettings(settings: AgentSettings): Promise<void> {
    try {
      await this.request('/settings', {
        method: 'PUT',
        body: JSON.stringify(settings),
      });
    } catch (error) {
      // Fallback to localStorage for demo
      localStorage.setItem('agentSettings', JSON.stringify(settings));
    }
  }

  async updateAgentSpecificSettings(agentType: keyof AgentSettings, settings: Partial<AgentSettings[keyof AgentSettings]>): Promise<void> {
    try {
      await this.request(`/settings/${agentType}`, {
        method: 'PATCH',
        body: JSON.stringify(settings),
      });
    } catch (error) {
      // Fallback to localStorage for demo
      const stored = localStorage.getItem('agentSettings');
      const currentSettings = stored ? JSON.parse(stored) : this.getDefaultSettings();
      currentSettings[agentType] = { ...currentSettings[agentType], ...settings };
      localStorage.setItem('agentSettings', JSON.stringify(currentSettings));
    }
  }

  // Trading Operations
  async executeTradeSignal(agentId: string, signal: TradeSignal): Promise<void> {
    try {
      await this.request(`/agents/${agentId}/trade`, {
        method: 'POST',
        body: JSON.stringify(signal),
      });
    } catch (error) {
      console.log('Trade signal would be executed:', { agentId, signal });
      // In demo mode, just log the trade signal
    }
  }

  async getAgentPerformance(agentId: string, timeframe: string = '30d'): Promise<AgentPerformance> {
    try {
      return await this.request(`/agents/${agentId}/performance?timeframe=${timeframe}`);
    } catch (error) {
      // Return mock performance data for demo
      return {
        totalReturn: Math.random() * 20 - 5, // -5% to +15%
        winRate: Math.random() * 30 + 60, // 60% to 90%
        sharpeRatio: Math.random() * 2 + 0.5, // 0.5 to 2.5
        maxDrawdown: Math.random() * -10 - 2, // -2% to -12%
        totalTrades: Math.floor(Math.random() * 100) + 20,
      };
    }
  }

  // Helper method for default settings
  private getDefaultSettings(): AgentSettings {
    return {
      nexus: {
        strategyAggressiveness: 'balanced',
        tradingStyle: 'day',
        enableTechnicalAnalysis: true,
        enableFundamentalAnalysis: false,
        maxPositionSize: 5000,
        positionSizeType: 'usd',
        dailyLossLimit: 500,
        dailyLossAction: 'alert',
        assetsToAvoid: [],
        maxAssetWeight: 20,
        rebalancingFrequency: 'monthly',
        enableSmartAllocation: true,
        customRules: '',
      },
      oracle: {
        strategyAggressiveness: 'balanced',
        predictionTimeframe: 'medium',
        enableMarketSentiment: true,
        enableNewsAnalysis: true,
        maxPositionSize: 5000,
        positionSizeType: 'usd',
        dailyLossLimit: 500,
        dailyLossAction: 'alert',
        assetsToAvoid: [],
        maxAssetWeight: 20,
        rebalancingFrequency: 'monthly',
        enableSmartAllocation: true,
        customRules: '',
      },
      echo: {
        strategyAggressiveness: 'balanced',
        enableAlgorithmicTrading: true,
        enableBacktesting: true,
        enablePaperTrading: false,
        maxPositionSize: 5000,
        positionSizeType: 'usd',
        dailyLossLimit: 500,
        dailyLossAction: 'alert',
        assetsToAvoid: [],
        maxAssetWeight: 20,
        rebalancingFrequency: 'monthly',
        enableSmartAllocation: true,
        customRules: '',
      },
      sentinel: {
        strategyAggressiveness: 'conservative',
        enableComplianceChecks: true,
        enableAuditTrail: true,
        enableRiskMonitoring: true,
        maxPositionSize: 5000,
        positionSizeType: 'usd',
        dailyLossLimit: 500,
        dailyLossAction: 'block',
        assetsToAvoid: [],
        maxAssetWeight: 15,
        rebalancingFrequency: 'monthly',
        enableSmartAllocation: true,
        customRules: '',
      },
    };
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Utility functions for common operations
export const agentApi = {
  // Get all user agents
  getUserAgents: () => apiClient.getUserAgents(),
  
  // Add a new agent
  addAgent: (agentData: Omit<UserAgent, 'id' | 'dateAdded'>) => apiClient.addAgent(agentData),
  
  // Remove an agent
  removeAgent: (agentId: string) => apiClient.removeAgent(agentId),
  
  // Toggle agent active status
  toggleAgent: (agentId: string, isActive: boolean) => apiClient.updateAgentStatus(agentId, isActive),
  
  // Get agent settings
  getSettings: () => apiClient.getAgentSettings(),
  
  // Update all settings
  updateSettings: (settings: AgentSettings) => apiClient.updateAgentSettings(settings),
  
  // Update specific agent settings
  updateAgentSettings: (agentType: keyof AgentSettings, settings: Partial<AgentSettings[keyof AgentSettings]>) => 
    apiClient.updateAgentSpecificSettings(agentType, settings),
  
  // Execute trade signal
  executeTrade: (agentId: string, signal: any) => apiClient.executeTradeSignal(agentId, signal),
  
  // Get performance data
  getPerformance: (agentId: string, timeframe?: string) => apiClient.getAgentPerformance(agentId, timeframe),
}; 